package com.sqx.modules.patientInfo.util;

import com.sqx.common.utils.Result;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Pattern;

/**
 * <p>
 * 医疗数据验证工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public class MedicalDataValidator {

    // 常用正则表达式
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    private static final Pattern DATETIME_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    private static final Pattern BLOOD_TYPE_PATTERN = Pattern.compile("^(A|B|AB|O)$");
    private static final Pattern ICD10_PATTERN = Pattern.compile("^[A-Z]\\d{2}(\\.\\d{1,2})?$");

    /**
     * 验证日期格式
     */
    public static Result validateDate(String date, String fieldName) {
        if (!StringUtils.hasText(date)) {
            return Result.success();
        }
        
        if (!DATE_PATTERN.matcher(date).matches()) {
            return Result.error(fieldName + "格式错误，应为yyyy-MM-dd格式");
        }
        
        try {
            LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (DateTimeParseException e) {
            return Result.error(fieldName + "日期无效");
        }
        
        return Result.success();
    }

    /**
     * 验证日期时间格式
     */
    public static Result validateDateTime(String dateTime, String fieldName) {
        if (!StringUtils.hasText(dateTime)) {
            return Result.success();
        }
        
        if (!DATETIME_PATTERN.matcher(dateTime).matches()) {
            return Result.error(fieldName + "格式错误，应为yyyy-MM-dd HH:mm:ss格式");
        }
        
        return Result.success();
    }

    /**
     * 验证手机号码
     */
    public static Result validatePhone(String phone, String fieldName) {
        if (!StringUtils.hasText(phone)) {
            return Result.success();
        }
        
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return Result.error(fieldName + "格式错误");
        }
        
        return Result.success();
    }

    /**
     * 验证身份证号码
     */
    public static Result validateIdCard(String idCard, String fieldName) {
        if (!StringUtils.hasText(idCard)) {
            return Result.success();
        }
        
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return Result.error(fieldName + "格式错误");
        }
        
        return Result.success();
    }

    /**
     * 验证血型
     */
    public static Result validateBloodType(String bloodType, String fieldName) {
        if (!StringUtils.hasText(bloodType)) {
            return Result.success();
        }
        
        if (!BLOOD_TYPE_PATTERN.matcher(bloodType).matches()) {
            return Result.error(fieldName + "必须是A、B、AB、O中的一种");
        }
        
        return Result.success();
    }

    /**
     * 验证ICD-10疾病编码
     */
    public static Result validateICD10Code(String code, String fieldName) {
        if (!StringUtils.hasText(code)) {
            return Result.success();
        }
        
        if (!ICD10_PATTERN.matcher(code).matches()) {
            return Result.error(fieldName + "格式错误，应符合ICD-10编码规范");
        }
        
        return Result.success();
    }

    /**
     * 验证生命体征数值范围
     */
    public static class VitalSignsValidator {
        
        public static Result validateBloodPressure(Integer systolic, Integer diastolic) {
            if (systolic != null && diastolic != null) {
                if (systolic <= diastolic) {
                    return Result.error("收缩压必须大于舒张压");
                }
            }
            
            if (systolic != null && (systolic < 50 || systolic > 300)) {
                return Result.error("收缩压范围应在50-300mmHg之间");
            }
            
            if (diastolic != null && (diastolic < 30 || diastolic > 200)) {
                return Result.error("舒张压范围应在30-200mmHg之间");
            }
            
            return Result.success();
        }
        
        public static Result validateHeartRate(Integer heartRate) {
            if (heartRate != null && (heartRate < 30 || heartRate > 250)) {
                return Result.error("心率范围应在30-250次/分之间");
            }
            return Result.success();
        }
        
        public static Result validateTemperature(BigDecimal temperature) {
            if (temperature != null && (temperature.compareTo(new BigDecimal("30")) < 0 || 
                                       temperature.compareTo(new BigDecimal("45")) > 0)) {
                return Result.error("体温范围应在30-45℃之间");
            }
            return Result.success();
        }
        
        public static Result validateOxygenSaturation(BigDecimal oxygenSaturation) {
            if (oxygenSaturation != null && (oxygenSaturation.compareTo(new BigDecimal("50")) < 0 || 
                                           oxygenSaturation.compareTo(new BigDecimal("100")) > 0)) {
                return Result.error("血氧饱和度范围应在50-100%之间");
            }
            return Result.success();
        }
    }

    /**
     * 验证身体指标
     */
    public static class PhysicalIndicatorValidator {
        
        public static Result validateHeight(BigDecimal height) {
            if (height != null && (height.compareTo(new BigDecimal("50")) < 0 || 
                                  height.compareTo(new BigDecimal("250")) > 0)) {
                return Result.error("身高范围应在50-250cm之间");
            }
            return Result.success();
        }
        
        public static Result validateWeight(BigDecimal weight) {
            if (weight != null && (weight.compareTo(new BigDecimal("10")) < 0 || 
                                  weight.compareTo(new BigDecimal("300")) > 0)) {
                return Result.error("体重范围应在10-300kg之间");
            }
            return Result.success();
        }
        
        public static Result validateBMI(BigDecimal height, BigDecimal weight) {
            if (height != null && weight != null) {
                BigDecimal heightInMeters = height.divide(new BigDecimal("100"));
                BigDecimal bmi = weight.divide(heightInMeters.multiply(heightInMeters), 2, BigDecimal.ROUND_HALF_UP);
                
                if (bmi.compareTo(new BigDecimal("10")) < 0 || bmi.compareTo(new BigDecimal("50")) > 0) {
                    return Result.error("根据身高体重计算的BMI值异常，请检查身高体重数据");
                }
            }
            return Result.success();
        }
    }

    /**
     * 验证药物剂量
     */
    public static class MedicationValidator {
        
        public static Result validateDosage(BigDecimal dosage, String unit) {
            if (dosage != null && dosage.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("药物剂量必须大于0");
            }
            
            if (dosage != null && dosage.compareTo(new BigDecimal("10000")) > 0) {
                return Result.error("药物剂量过大，请检查数据");
            }
            
            return Result.success();
        }
        
        public static Result validateDateRange(String startDate, String endDate) {
            if (StringUtils.hasText(startDate) && StringUtils.hasText(endDate)) {
                try {
                    LocalDate start = LocalDate.parse(startDate);
                    LocalDate end = LocalDate.parse(endDate);
                    
                    if (end.isBefore(start)) {
                        return Result.error("结束日期不能早于开始日期");
                    }
                } catch (DateTimeParseException e) {
                    return Result.error("日期格式错误");
                }
            }
            
            return Result.success();
        }
    }

    /**
     * 验证过敏信息
     */
    public static class AllergyValidator {
        
        public static Result validateSeverity(Integer severity) {
            if (severity != null && (severity < 1 || severity > 4)) {
                return Result.error("过敏严重程度值必须在1-4之间");
            }
            return Result.success();
        }
        
        public static Result validateAllergenName(String allergenName) {
            if (!StringUtils.hasText(allergenName)) {
                return Result.error("过敏原名称不能为空");
            }
            
            if (allergenName.length() > 200) {
                return Result.error("过敏原名称不能超过200字符");
            }
            
            return Result.success();
        }
    }

    /**
     * 验证治疗方案
     */
    public static class TreatmentPlanValidator {
        
        public static Result validateProgress(Integer progress) {
            if (progress != null && (progress < 0 || progress > 100)) {
                return Result.error("治疗进度必须在0-100%之间");
            }
            return Result.success();
        }
        
        public static Result validatePlanDates(String planDate, String startDate, String endDate) {
            try {
                LocalDate plan = planDate != null ? LocalDate.parse(planDate) : null;
                LocalDate start = startDate != null ? LocalDate.parse(startDate) : null;
                LocalDate end = endDate != null ? LocalDate.parse(endDate) : null;
                
                if (plan != null && start != null && start.isBefore(plan)) {
                    return Result.error("计划开始日期不能早于制定日期");
                }
                
                if (start != null && end != null && end.isBefore(start)) {
                    return Result.error("计划结束日期不能早于开始日期");
                }
                
            } catch (DateTimeParseException e) {
                return Result.error("日期格式错误");
            }
            
            return Result.success();
        }
    }

    /**
     * 验证敏感信息脱敏
     */
    public static String maskSensitiveInfo(String info, String type) {
        if (!StringUtils.hasText(info)) {
            return info;
        }
        
        switch (type.toLowerCase()) {
            case "phone":
                return info.length() >= 11 ? info.substring(0, 3) + "****" + info.substring(7) : info;
            case "idcard":
                return info.length() >= 18 ? info.substring(0, 6) + "********" + info.substring(14) : info;
            case "name":
                return info.length() > 1 ? info.substring(0, 1) + "*".repeat(info.length() - 1) : info;
            default:
                return info;
        }
    }
}
