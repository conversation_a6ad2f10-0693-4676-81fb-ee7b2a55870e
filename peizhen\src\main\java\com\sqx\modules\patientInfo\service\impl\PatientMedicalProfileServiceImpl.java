package com.sqx.modules.patientInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.patientInfo.dao.*;
import com.sqx.modules.patientInfo.entity.*;
import com.sqx.modules.patientInfo.service.PatientMedicalProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 患者医疗档案综合服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Service
public class PatientMedicalProfileServiceImpl implements PatientMedicalProfileService {

    @Autowired
    private PatientInfoDao patientInfoDao;
    
    @Autowired
    private PatientHealthStatusDao healthStatusDao;
    
    @Autowired
    private PatientMedicalHistoryDao medicalHistoryDao;
    
    @Autowired
    private PatientAllergyDao allergyDao;
    
    @Autowired
    private PatientMedicationDao medicationDao;
    
    @Autowired
    private PatientVitalSignsDao vitalSignsDao;
    
    @Autowired
    private PatientTestResultDao testResultDao;
    
    @Autowired
    private PatientVaccinationDao vaccinationDao;
    
    @Autowired
    private PatientTreatmentPlanDao treatmentPlanDao;

    // ==================== 综合医疗档案管理 ====================
    
    @Override
    public Result getCompleteMedicalProfile(Long patientId) {
        try {
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            // 获取健康状态
            PatientHealthStatus healthStatus = healthStatusDao.getByPatientId(patientId);
            patientInfo.setHealthStatus(healthStatus);

            // 获取活跃病史
            List<PatientMedicalHistory> medicalHistoryList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
            patientInfo.setMedicalHistoryList(medicalHistoryList);

            // 获取活跃过敏记录
            List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
            patientInfo.setAllergyList(allergyList);

            // 获取当前用药
            List<PatientMedication> currentMedicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
            patientInfo.setCurrentMedicationList(currentMedicationList);

            // 获取最新生命体征
            PatientVitalSigns latestVitalSigns = vitalSignsDao.getLatestByPatient(patientId);
            patientInfo.setLatestVitalSigns(latestVitalSigns);

            // 获取最近检查结果
            List<PatientTestResult> recentTestResults = testResultDao.getRecentByPatient(patientId, 10);
            patientInfo.setRecentTestResults(recentTestResults);

            // 获取当前治疗方案
            List<PatientTreatmentPlan> currentTreatmentPlans = treatmentPlanDao.getCurrentByPatient(patientId);
            patientInfo.setCurrentTreatmentPlans(currentTreatmentPlans);

            // 获取疫苗接种记录
            List<PatientVaccination> vaccinationList = vaccinationDao.getByPatient(patientId);
            patientInfo.setVaccinationList(vaccinationList);

            return Result.success().put("data", patientInfo);
        } catch (Exception e) {
            return Result.error("获取医疗档案失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result updateProfileCompleteness(Long patientId) {
        try {
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }

            int completeness = calculateProfileCompleteness(patientId);
            patientInfo.setProfileCompleteness(completeness);
            patientInfo.setLastMedicalUpdateTime(LocalDateTime.now());
            
            patientInfoDao.updateById(patientInfo);
            return Result.success().put("completeness", completeness);
        } catch (Exception e) {
            return Result.error("更新档案完整度失败：" + e.getMessage());
        }
    }

    @Override
    public Result assessMedicalRisk(Long patientId) {
        try {
            int riskScore = calculateRiskScore(patientId);
            int riskLevel = 1; // 默认低风险
            
            if (riskScore >= 70) {
                riskLevel = 3; // 高风险
            } else if (riskScore >= 40) {
                riskLevel = 2; // 中风险
            }

            // 更新患者风险等级
            PatientInfo patientInfo = patientInfoDao.selectById(patientId);
            if (patientInfo != null) {
                patientInfo.setRiskLevel(riskLevel);
                patientInfoDao.updateById(patientInfo);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("riskScore", riskScore);
            result.put("riskLevel", riskLevel);
            result.put("riskDescription", getRiskDescription(riskLevel));
            
            return Result.success().put("data", result);
        } catch (Exception e) {
            return Result.error("风险评估失败：" + e.getMessage());
        }
    }

    // ==================== 健康状态管理 ====================
    
    @Override
    @Transactional
    public Result saveHealthStatus(PatientHealthStatus healthStatus) {
        try {
            if (healthStatus.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }

            // 计算BMI
            if (healthStatus.getHeight() != null && healthStatus.getWeight() != null) {
                double heightInMeters = healthStatus.getHeight().doubleValue() / 100;
                double bmi = healthStatus.getWeight().doubleValue() / (heightInMeters * heightInMeters);
                healthStatus.setBmi(java.math.BigDecimal.valueOf(Math.round(bmi * 100.0) / 100.0));
            }

            // 检查是否已存在健康状态记录
            PatientHealthStatus existing = healthStatusDao.getByPatientId(healthStatus.getPatientId());
            if (existing != null) {
                healthStatus.setStatusId(existing.getStatusId());
                healthStatusDao.updateById(healthStatus);
            } else {
                healthStatusDao.insert(healthStatus);
            }

            // 更新档案完整度
            updateProfileCompleteness(healthStatus.getPatientId());
            
            return Result.success("健康状态保存成功");
        } catch (Exception e) {
            return Result.error("保存健康状态失败：" + e.getMessage());
        }
    }

    @Override
    public PatientHealthStatus getHealthStatusByPatient(Long patientId) {
        return healthStatusDao.getByPatientId(patientId);
    }

    // ==================== 病史管理 ====================
    
    @Override
    @Transactional
    public Result saveMedicalHistory(PatientMedicalHistory medicalHistory) {
        try {
            if (medicalHistory.getPatientId() == null) {
                return Result.error("患者ID不能为空");
            }
            if (medicalHistory.getDiseaseName() == null || medicalHistory.getDiseaseName().trim().isEmpty()) {
                return Result.error("疾病名称不能为空");
            }

            if (medicalHistory.getHistoryId() == null) {
                medicalHistoryDao.insert(medicalHistory);
            } else {
                medicalHistoryDao.updateById(medicalHistory);
            }

            // 更新档案完整度
            updateProfileCompleteness(medicalHistory.getPatientId());
            
            return Result.success("病史记录保存成功");
        } catch (Exception e) {
            return Result.error("保存病史记录失败：" + e.getMessage());
        }
    }

    @Override
    public IPage<PatientMedicalHistory> getMedicalHistoryList(Integer page, Integer limit, Long patientId, Integer historyType) {
        Page<PatientMedicalHistory> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return medicalHistoryDao.getHistoryList(pages, patientId, historyType);
    }

    @Override
    public List<PatientMedicalHistory> getActiveMedicalHistory(Long patientId) {
        return medicalHistoryDao.getActiveHistoryByPatient(patientId);
    }

    @Override
    @Transactional
    public Result deleteMedicalHistory(Long historyId) {
        try {
            PatientMedicalHistory medicalHistory = medicalHistoryDao.selectById(historyId);
            if (medicalHistory == null) {
                return Result.error("病史记录不存在");
            }
            
            medicalHistory.setIsDelete(1);
            medicalHistoryDao.updateById(medicalHistory);
            
            // 更新档案完整度
            updateProfileCompleteness(medicalHistory.getPatientId());
            
            return Result.success("病史记录删除成功");
        } catch (Exception e) {
            return Result.error("删除病史记录失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================
    
    /**
     * 计算档案完整度
     */
    private int calculateProfileCompleteness(Long patientId) {
        int totalFields = 10; // 总评估项目数
        int completedFields = 0;

        PatientInfo patientInfo = patientInfoDao.selectById(patientId);
        if (patientInfo != null) {
            // 基本信息完整度
            if (patientInfo.getRealName() != null && !patientInfo.getRealName().trim().isEmpty()) completedFields++;
            if (patientInfo.getPhone() != null && !patientInfo.getPhone().trim().isEmpty()) completedFields++;
            if (patientInfo.getIdNumber() != null && !patientInfo.getIdNumber().trim().isEmpty()) completedFields++;
        }

        // 健康状态完整度
        PatientHealthStatus healthStatus = healthStatusDao.getByPatientId(patientId);
        if (healthStatus != null) completedFields++;

        // 病史记录完整度
        List<PatientMedicalHistory> historyList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
        if (historyList != null && !historyList.isEmpty()) completedFields++;

        // 过敏记录完整度
        List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
        if (allergyList != null && !allergyList.isEmpty()) completedFields++;

        // 用药记录完整度
        List<PatientMedication> medicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
        if (medicationList != null && !medicationList.isEmpty()) completedFields++;

        // 生命体征完整度
        PatientVitalSigns vitalSigns = vitalSignsDao.getLatestByPatient(patientId);
        if (vitalSigns != null) completedFields++;

        // 检查结果完整度
        List<PatientTestResult> testResults = testResultDao.getRecentByPatient(patientId, 1);
        if (testResults != null && !testResults.isEmpty()) completedFields++;

        // 疫苗接种完整度
        List<PatientVaccination> vaccinations = vaccinationDao.getByPatient(patientId);
        if (vaccinations != null && !vaccinations.isEmpty()) completedFields++;

        return (int) Math.round((double) completedFields / totalFields * 100);
    }

    /**
     * 计算风险评分
     */
    private int calculateRiskScore(Long patientId) {
        int riskScore = 0;

        // 基于年龄的风险评分
        PatientInfo patientInfo = patientInfoDao.selectById(patientId);
        if (patientInfo != null && patientInfo.getIsUnderAge() != null && patientInfo.getIsUnderAge() == 0) {
            riskScore += 10; // 成年人基础风险
        }

        // 基于病史的风险评分
        List<PatientMedicalHistory> historyList = medicalHistoryDao.getActiveHistoryByPatient(patientId);
        if (historyList != null) {
            riskScore += historyList.size() * 5; // 每个活跃病史增加5分
        }

        // 基于过敏的风险评分
        List<PatientAllergy> allergyList = allergyDao.getActiveAllergiesByPatient(patientId);
        if (allergyList != null) {
            for (PatientAllergy allergy : allergyList) {
                if (allergy.getSeverity() != null) {
                    riskScore += allergy.getSeverity() * 3; // 严重程度越高风险越大
                }
            }
        }

        // 基于用药的风险评分
        List<PatientMedication> medicationList = medicationDao.getCurrentMedicationsByPatient(patientId);
        if (medicationList != null) {
            riskScore += medicationList.size() * 2; // 每个用药增加2分
        }

        return Math.min(riskScore, 100); // 最高100分
    }

    /**
     * 获取风险等级描述
     */
    private String getRiskDescription(int riskLevel) {
        switch (riskLevel) {
            case 1:
                return "低风险：患者整体健康状况良好，无重大医疗风险";
            case 2:
                return "中风险：患者存在一定医疗风险，需要定期监测和关注";
            case 3:
                return "高风险：患者存在较高医疗风险，需要密切监测和专业医疗管理";
            default:
                return "未知风险等级";
        }
    }
}
